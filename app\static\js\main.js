
// =================================================================================
// 1. GLOBAL VARIABLES & CONFIGURATION
// =================================================================================

// Suppress console errors from browser extensions
const originalConsoleError = console.error;
console.error = function(...args) {
    const message = args.join(' ');
    if (message.includes('iterable') || message.includes('Iterable')) {
        return;
    }
    originalConsoleError.apply(console, args);
};

// Configure marked.js for markdown rendering
marked.setOptions({
    breaks: true,
    gfm: true,
    headerIds: false,
    mangle: false,
    sanitize: false,
    renderer: (function() {
        const renderer = new marked.Renderer();
        renderer.link = function(href, title, text) {
            const link = marked.Renderer.prototype.link.call(this, href, title, text);
            return link.replace('<a ', '<a target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:underline" ');
        };
        return renderer;
    })()
});

// --- Global State Variables ---
let isDarkMode = localStorage.getItem('theme') === 'dark';
let clientName = localStorage.getItem('clientName') || '';
let sessionId = localStorage.getItem('sessionId') || '';
let sessionStart = localStorage.getItem('sessionStart') || '';
let deviceFingerprint;
let availableModels = [];
let selectedModel = localStorage.getItem('selectedModel') || '';


// =================================================================================
// 2. FUNCTION DEFINITIONS
// =================================================================================

/**
 * Generates a robust device fingerprint for user identification.
 */
function generateDeviceFingerprint() {
    const screenInfo = `${window.screen.width}x${window.screen.height}x${window.screen.colorDepth}`;
    const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const language = navigator.language;
    const platform = navigator.platform;
    const userAgent = navigator.userAgent;
    const cpuCores = navigator.hardwareConcurrency || 'unknown';
    const deviceMemory = navigator.deviceMemory || 'unknown';
    const doNotTrack = navigator.doNotTrack || 'unknown';
    const cookiesEnabled = navigator.cookieEnabled;

    let canvasData = 'unknown';
    try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = 200;
        canvas.height = 50;
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillStyle = '#1a73e8';
        ctx.fillText('ERDB Knowledge Products', 2, 2);
        ctx.fillStyle = '#e8731a';
        ctx.fillRect(100, 25, 80, 20);
        canvasData = canvas.toDataURL().substring(0, 100);
    } catch (e) {
        console.error('Canvas fingerprinting failed:', e);
    }

    const fingerprint = `${screenInfo}-${timeZone}-${language}-${platform}-${cpuCores}-${deviceMemory}-${doNotTrack}-${cookiesEnabled}-${canvasData}-${userAgent.substring(0, 50)}`;
    let hash = 0;
    for (let i = 0; i < fingerprint.length; i++) {
        const char = fingerprint.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
    }
    const base64 = btoa(fingerprint).replace(/[^a-zA-Z0-9]/g, '');
    return `${Math.abs(hash).toString(16)}-${base64.substring(0, 16)}`;
}

/**
 * Fetches available AI models from the API and populates the selector.
 */
async function loadAvailableModels() {
    const modelSelector = document.getElementById('model-selector');
    if (!modelSelector) return;

    try {
        modelSelector.classList.add('model-selector-loading');
        modelSelector.innerHTML = '<option value="">Loading models...</option>';
        const response = await fetch('/api/available-models');
        const result = await response.json();

        if (result.success && result.models) {
            availableModels = result.models;
            populateModelSelector(result.models, result.default_model);
            if (!selectedModel && result.default_model) {
                selectedModel = result.default_model;
                localStorage.setItem('selectedModel', selectedModel);
            }
            updateModelInfo();
        } else {
            throw new Error(result.error || 'Failed to load models');
        }
    } catch (error) {
        console.error('Error loading models:', error);
        const fallbackModels = [{
            name: 'llama3.1:8b-instruct-q4_K_M',
            display_name: 'Llama 3.1 8B',
            description: 'Fast, efficient model for general queries',
            capability: 'General Purpose',
            size_formatted: '4.9 GB'
        }];
        availableModels = fallbackModels;
        populateModelSelector(fallbackModels, 'llama3.1:8b-instruct-q4_K_M');
        if (!selectedModel) {
            selectedModel = 'llama3.1:8b-instruct-q4_K_M';
            localStorage.setItem('selectedModel', selectedModel);
        }
        updateModelInfo();
    } finally {
        if (modelSelector) {
            modelSelector.classList.remove('model-selector-loading');
        }
    }
}

/**
 * Populates the model selector dropdown with a list of models.
 */
function populateModelSelector(models, defaultModel) {
    const modelSelector = document.getElementById('model-selector');
    if (!modelSelector) return;
    modelSelector.innerHTML = '';

    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = '- Select AI Model -';
    modelSelector.appendChild(defaultOption);

    models.forEach(model => {
        const option = document.createElement('option');
        option.value = model.name;
        option.textContent = `${model.display_name} (${model.size_formatted})`;
        option.setAttribute('data-description', model.description);
        option.setAttribute('data-capability', model.capability);
        option.setAttribute('data-size', model.size_formatted);
        if (model.name === selectedModel || (model.name === defaultModel && !selectedModel)) {
            option.selected = true;
            selectedModel = model.name;
        }
        modelSelector.appendChild(option);
    });

    modelSelector.addEventListener('change', handleModelSelection);
}

/**
 * Handles changes to the model selector.
 */
function handleModelSelection(event) {
    const selectedValue = event.target.value;
    if (selectedValue) {
        selectedModel = selectedValue;
        localStorage.setItem('selectedModel', selectedModel);
    } else {
        selectedModel = '';
        localStorage.removeItem('selectedModel');
    }
    updateModelInfo();
}

/**
 * Updates the UI to display information about the selected model.
 */
function updateModelInfo() {
    const modelInfo = document.getElementById('model-info');
    const modelDescription = document.getElementById('model-description');
    const modelSize = document.getElementById('model-size');
    if (!modelInfo || !modelDescription || !modelSize) return;

    if (!selectedModel) {
        modelInfo.style.display = 'none';
        return;
    }

    const model = availableModels.find(m => m.name === selectedModel);
    if (model) {
        modelDescription.innerHTML = `<span class="model-capability">${model.capability}</span><br>${model.description}`;
        modelSize.textContent = `Size: ${model.size_formatted}`;
        modelInfo.style.display = 'block';
    } else {
        modelInfo.style.display = 'none';
    }
}

/**
 * Saves the current chat history to localStorage.
 */
function saveChatHistory() {
    const chatBox = document.getElementById('chat-box');
    if (chatBox && chatBox.innerHTML.trim() !== '') {
        try {
            const historyKey = `chatHistory_${deviceFingerprint}`;
            localStorage.setItem(historyKey, chatBox.innerHTML);
            localStorage.setItem('chatHistory', chatBox.innerHTML); // For backward compatibility
            localStorage.setItem(`chatHistoryUpdated_${deviceFingerprint}`, new Date().toISOString());
        } catch (error) {
            console.error('Error saving chat history:', error);
            if (error.name === 'QuotaExceededError') {
                try {
                    for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        if (key && key.startsWith('chatHistory_') && !key.includes(deviceFingerprint)) {
                            localStorage.removeItem(key);
                        }
                    }
                    localStorage.setItem(`chatHistory_${deviceFingerprint}`, chatBox.innerHTML);
                } catch (retryError) {
                    console.error('Failed to save chat history after cleanup:', retryError);
                }
            }
        }
    }
}

/**
 * Loads chat history from localStorage into the chat box.
 */
function loadChatHistory() {
    const chatBox = document.getElementById('chat-box');
    if (!chatBox) return false;

    try {
        const historyKey = `chatHistory_${deviceFingerprint}`;
        let savedHistory = localStorage.getItem(historyKey) || localStorage.getItem('chatHistory');

        if (savedHistory && savedHistory.trim() !== '') {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = savedHistory;
            const hasMessages = tempDiv.querySelectorAll('.flex.justify-start, .flex.justify-end').length > 0;

            if (hasMessages) {
                chatBox.innerHTML = savedHistory;
                chatBox.scrollTop = chatBox.scrollHeight;
                
                setTimeout(() => {
                    try {
                        if (typeof VoiceInterface !== 'undefined' && VoiceInterface.state && VoiceInterface.state.isSupported) {
                            const aiMessages = chatBox.querySelectorAll('.welcome-message');
                            aiMessages.forEach((message, index) => {
                                if (message.querySelector('.speech-control-btn')) return;
                                const messageId = `restored_ai_${index}_${Date.now()}`;
                                const messageText = VoiceInterface.extractMessageText(message);
                                if (messageText && messageText.length > 0) {
                                    VoiceInterface.addSpeechControls(message, messageId, messageText);
                                }
                            });
                        }
                    } catch (error) {
                        console.error('Error adding voice controls to restored messages:', error);
                    }
                }, 500);
                return true;
            }
        }
    } catch (error) {
        console.error('Error loading chat history:', error);
        try {
            localStorage.removeItem(`chatHistory_${deviceFingerprint}`);
            localStorage.removeItem('chatHistory');
        } catch (clearError) {
            console.error('Error clearing corrupted history:', clearError);
        }
    }
    return false;
}

/**
 * Toggles the color theme between light and dark mode.
 */
function toggleTheme() {
    isDarkMode = !isDarkMode;
    DMSUtils.toggleDarkMode(isDarkMode);
    applyTheme();
    applyThemeToRestoredContent();
}

/**
 * Applies the current color theme to the entire page.
 */
function applyTheme() {
    // This function can be expanded if more specific theme changes are needed
    // beyond what the core CSS handles.
}

/**
 * Applies the current color theme to dynamically loaded chat content.
 */
function applyThemeToRestoredContent() {
    // This function can be expanded if restored content needs special theme handling.
}

/**
 * Displays the modal to ask for the user's name.
 */
function showClientNameModal() {
    const modal = document.getElementById('clientNameModal');
    const nameInput = document.getElementById('clientNameInput');
    if (modal && nameInput) {
        if (clientName) {
            nameInput.value = clientName;
        }
        modal.classList.remove('hidden');
        setTimeout(() => nameInput.focus(), 100);
    }
}

/**
 * Saves the client's name from the modal and initializes the chat.
 */
function saveClientName() {
    const nameInput = document.getElementById('clientNameInput');
    if (!nameInput) return;
    const name = nameInput.value.trim();

    if (name) {
        clientName = name;
        localStorage.setItem('clientName', clientName);
        document.getElementById('clientNameModal').classList.add('hidden');
        
        const logoutButton = document.getElementById('logoutButton');
        if (logoutButton) {
            logoutButton.textContent = `Logout (${clientName})`;
        }
        nameInput.value = '';

        // Add welcome message
        const chatBox = document.getElementById('chat-box');
        if (chatBox) {
            const welcomeMessage = document.createElement('div');
            welcomeMessage.className = 'welcome-message';
            welcomeMessage.innerHTML = `
                <div class="welcome-message-header">
                    <svg class="welcome-message-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" /></svg>
                    <h3 class="welcome-message-title">EAIA</h3>
                    <span class="welcome-message-time">${new Date().toLocaleTimeString()}</span>
                </div>
                <div class="welcome-message-content" id="welcome-content-${Date.now()}">
                    <p>To get started:</p>
                    <ol>
                        <li>Select a category from the dropdown menu below</li>
                        <li>Type your question in the text box</li>
                        <li>Press Enter or click the arrow button to submit</li>
                    </ol>
                    <p>My answers will be based on the available ERDB Knowledge Products in the selected category. How can I help you today?</p>
                </div>`;
            chatBox.appendChild(welcomeMessage);
            saveChatHistory();
        }
    } else {
        alert('Please enter your name to continue.');
    }
}

/**
 * Ends the user's session on the server.
 */
function endSession() {
    if (sessionId) {
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        fetch(`/admin/session/${sessionId}/close`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json', 'X-CSRFToken': csrfToken }
        }).catch(error => console.error('Error closing session:', error));
    }
}

/**
 * Clears all session and chat history data from localStorage.
 */
function clearAllSessionData() {
    const currentFingerprint = deviceFingerprint;
    localStorage.removeItem('clientName');
    localStorage.removeItem('sessionId');
    localStorage.removeItem('sessionStart');
    localStorage.removeItem('chatHistory');
    if (currentFingerprint) {
        localStorage.removeItem(`chatHistory_${currentFingerprint}`);
        localStorage.removeItem(`chatHistoryUpdated_${currentFingerprint}`);
    }
    clientName = '';
    sessionId = '';
    sessionStart = '';
    const chatBox = document.getElementById('chat-box');
    if (chatBox) {
        chatBox.innerHTML = '';
    }
    console.log('All chat history and session data cleared successfully');
}

/**
 * Handles the key press event for the query input.
 */
function handleKeyPress(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendQuery();
    }
}

/**
 * Sends the user's query to the server and displays the response.
 */
async function sendQuery() {
    const category = document.getElementById('category').value;
    const query = document.getElementById('query').value.trim();
    if (!category || !query) {
        alert('Please select a category and enter a query.');
        return;
    }
    if (!selectedModel) {
        alert('Please select an AI model before submitting your query.');
        return;
    }

    const antiHallucinationMode = document.querySelector('input[name="anti_hallucination_mode"]:checked').value;
    const selectedModelObj = availableModels.find(m => m.name === selectedModel);
    const modelDisplayName = selectedModelObj ? selectedModelObj.display_name : selectedModel;
    const chatBox = document.getElementById('chat-box');

    // Display user message
    const userMessage = document.createElement('div');
    userMessage.className = 'flex justify-start mb-4';
    userMessage.innerHTML = `
        <div class="max-w-2xl bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 shadow-sm border border-blue-200">
            <p class="text-gray-800">${query}</p>
        </div>`;
    chatBox.appendChild(userMessage);

    // Display typing indicator
    const typingIndicator = document.createElement('div');
    typingIndicator.id = 'typing-indicator';
    typingIndicator.className = 'flex justify-end mb-4';
    typingIndicator.innerHTML = `<div class="max-w-2xl bg-gray-50 rounded-lg p-4 shadow-sm border border-gray-200"><p class="text-gray-600 animate-pulse">EAIA is thinking...</p></div>`;
    chatBox.appendChild(typingIndicator);
    chatBox.scrollTop = chatBox.scrollHeight;

    try {
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const response = await fetch(`/query/${category}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json', 'X-CSRFToken': csrfToken },
            body: JSON.stringify({
                query,
                anti_hallucination_mode: antiHallucinationMode,
                selected_model: selectedModel,
                client_name: clientName,
                device_fingerprint: deviceFingerprint,
                session_id: sessionId,
                session_start: sessionStart
            })
        });
        const result = await response.json();

        if (result.session_id) sessionId = result.session_id;
        if (result.session_start) sessionStart = result.session_start;

        chatBox.removeChild(typingIndicator);

        const botMessage = document.createElement('div');
        botMessage.className = 'flex justify-end mb-4';
        
        if (result.error) {
            botMessage.innerHTML = `<div class="max-w-2xl bg-red-50 rounded-lg p-4 shadow-sm border border-red-200"><p class="text-red-600">Error: ${result.error}</p></div>`;
        } else {
            // Simplified response rendering
            botMessage.innerHTML = `
                <div class="welcome-message">
                    <div class="welcome-message-content">
                        ${marked.parse(result.answer)}
                    </div>
                </div>`;
        }
        chatBox.appendChild(botMessage);

    } catch (error) {
        if (chatBox.contains(typingIndicator)) {
            chatBox.removeChild(typingIndicator);
        }
        const errorMessage = document.createElement('div');
        errorMessage.className = 'flex justify-end mb-4';
        errorMessage.innerHTML = `<div class="max-w-2xl bg-red-50 rounded-lg p-4 shadow-sm border border-red-200"><p class="text-red-600">Error: ${error.message}</p></div>`;
        chatBox.appendChild(errorMessage);
    }

    chatBox.scrollTop = chatBox.scrollHeight;
    saveChatHistory();
}


// =================================================================================
// 3. INITIALIZATION & EVENT LISTENERS
// =================================================================================

document.addEventListener('DOMContentLoaded', function() {
    // Initialize device fingerprint
    deviceFingerprint = localStorage.getItem('deviceFingerprint') || generateDeviceFingerprint();
    localStorage.setItem('deviceFingerprint', deviceFingerprint);

    // Initialize UI components
    DMSUtils.initDarkMode();
    applyTheme();
    loadAvailableModels();

    // Check for existing user session
    const historyLoaded = loadChatHistory();
    if (clientName) {
        const logoutButton = document.getElementById('logoutButton');
        if (logoutButton) {
            logoutButton.textContent = `Logout (${clientName})`;
        }
        if (historyLoaded) {
            applyThemeToRestoredContent();
        }
    } else {
        setTimeout(showClientNameModal, 500);
    }
    
    // Add other event listeners
    window.addEventListener('beforeunload', saveChatHistory);
    window.addEventListener('pagehide', function(event) {
        saveChatHistory();
        if (!event.persisted) {
            endSession();
        }
    });
    window.addEventListener('visibilitychange', function() {
        if (document.visibilityState === 'hidden') {
            saveChatHistory();
        }
    });
});
